import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { DashboardLayout } from "@/components/dashboard-layout"
import { useUserRole } from "@/hooks/useUserRole"
import { GlowCard } from "@/components/ui/glow-card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { 
  Shield, 
  Database, 
  Users, 
  Settings2, 
  AlertTriangle,
  Crown,
  Server,
  Lock,
  DollarSign,
  UserPlus,
  CreditCard,
  Building,
  BarChart3,
  Zap,
  Globe,
  MessageSquare
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"

export default function ConfiguracoesOwner() {
  const navigate = useNavigate()
  const { isOwner, loading } = useUserRole()
  const { toast } = useToast()
  const [saving, setSaving] = useState(false)

  // Settings state
  const [settings, setSettings] = useState({
    maxUsers: 50,
    allowUserRegistration: false,
    requireEmailVerification: true,
    enableBackups: true,
    backupFrequency: 'daily',
    maintenanceMode: false,
    allowApiAccess: true,
    maxFileSize: 10,
    systemNotifications: true
  })

  // Redirect if not owner
  if (!loading && !isOwner) {
    navigate('/dashboard')
    return null
  }

  if (loading) {
    return (
      <DashboardLayout title="Configurações do Owner">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      // Simulate save operation
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast({
        title: "Configurações salvas",
        description: "As configurações do sistema foram atualizadas com sucesso.",
      })
    } catch (error) {
      toast({
        title: "Erro ao salvar",
        description: "Não foi possível salvar as configurações. Tente novamente.",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  return (
    <DashboardLayout title="Configurações do Owner">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-3">
          <Crown className="h-8 w-8 text-yellow-500" />
          <div>
            <h1 className="text-3xl font-bold">Configurações do Owner</h1>
            <p className="text-muted-foreground">
              Configurações avançadas do sistema - acesso restrito ao proprietário
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Sistema */}
          <GlowCard customSize className="w-full" glowColor="blue">
            <div className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <Server className="h-6 w-6 text-blue-500" />
                <h3 className="text-xl font-semibold">Sistema</h3>
                <Badge variant="secondary">Crítico</Badge>
              </div>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="maxUsers">Máximo de Usuários</Label>
                  <Input
                    id="maxUsers"
                    type="number"
                    value={settings.maxUsers}
                    onChange={(e) => setSettings({...settings, maxUsers: parseInt(e.target.value)})}
                    className="mt-1"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="maintenanceMode">Modo de Manutenção</Label>
                  <Switch
                    id="maintenanceMode"
                    checked={settings.maintenanceMode}
                    onCheckedChange={(checked) => setSettings({...settings, maintenanceMode: checked})}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="systemNotifications">Notificações do Sistema</Label>
                  <Switch
                    id="systemNotifications"
                    checked={settings.systemNotifications}
                    onCheckedChange={(checked) => setSettings({...settings, systemNotifications: checked})}
                  />
                </div>
              </div>
            </div>
          </GlowCard>

          {/* Segurança */}
          <GlowCard customSize className="w-full" glowColor="red">
            <div className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <Shield className="h-6 w-6 text-red-500" />
                <h3 className="text-xl font-semibold">Segurança</h3>
                <Badge variant="destructive">Alta</Badge>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="allowUserRegistration">Permitir Registro de Usuários</Label>
                  <Switch
                    id="allowUserRegistration"
                    checked={settings.allowUserRegistration}
                    onCheckedChange={(checked) => setSettings({...settings, allowUserRegistration: checked})}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="requireEmailVerification">Verificação de Email Obrigatória</Label>
                  <Switch
                    id="requireEmailVerification"
                    checked={settings.requireEmailVerification}
                    onCheckedChange={(checked) => setSettings({...settings, requireEmailVerification: checked})}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="allowApiAccess">Permitir Acesso à API</Label>
                  <Switch
                    id="allowApiAccess"
                    checked={settings.allowApiAccess}
                    onCheckedChange={(checked) => setSettings({...settings, allowApiAccess: checked})}
                  />
                </div>
              </div>
            </div>
          </GlowCard>

          {/* Backup */}
          <GlowCard customSize className="w-full" glowColor="green">
            <div className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <Database className="h-6 w-6 text-green-500" />
                <h3 className="text-xl font-semibold">Backup e Dados</h3>
                <Badge variant="outline">Automático</Badge>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="enableBackups">Backups Automáticos</Label>
                  <Switch
                    id="enableBackups"
                    checked={settings.enableBackups}
                    onCheckedChange={(checked) => setSettings({...settings, enableBackups: checked})}
                  />
                </div>

                <div>
                  <Label htmlFor="backupFrequency">Frequência de Backup</Label>
                  <select
                    id="backupFrequency"
                    value={settings.backupFrequency}
                    onChange={(e) => setSettings({...settings, backupFrequency: e.target.value})}
                    className="mt-1 w-full px-3 py-2 border border-input bg-background rounded-md"
                  >
                    <option value="hourly">A cada hora</option>
                    <option value="daily">Diário</option>
                    <option value="weekly">Semanal</option>
                  </select>
                </div>

                <div>
                  <Label htmlFor="maxFileSize">Tamanho Máximo de Arquivo (MB)</Label>
                  <Input
                    id="maxFileSize"
                    type="number"
                    value={settings.maxFileSize}
                    onChange={(e) => setSettings({...settings, maxFileSize: parseInt(e.target.value)})}
                    className="mt-1"
                  />
                </div>
              </div>
            </div>
          </GlowCard>

          {/* Gerenciar Assinantes */}
          <GlowCard customSize className="w-full" glowColor="blue">
            <div className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <UserPlus className="h-6 w-6 text-cyan-500" />
                <h3 className="text-xl font-semibold">Gerenciar Assinantes</h3>
                <Badge variant="secondary">134 ativos</Badge>
              </div>
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-sm font-medium">Total Assinantes</p>
                    <p className="text-2xl font-bold text-cyan-500">134</p>
                  </div>
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-sm font-medium">MRR</p>
                    <p className="text-2xl font-bold text-green-500">R$ 12.460</p>
                  </div>
                </div>

                <Button variant="outline" className="w-full">
                  Ver Todos Assinantes
                </Button>
              </div>
            </div>
          </GlowCard>

          {/* Planos de Assinatura */}
          <GlowCard customSize className="w-full" glowColor="purple">
            <div className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <CreditCard className="h-6 w-6 text-purple-500" />
                <h3 className="text-xl font-semibold">Planos de Assinatura</h3>
                <Badge>3 ativos</Badge>
              </div>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between items-center p-2 bg-muted rounded">
                    <span className="text-sm">Básico - R$ 49/mês</span>
                    <Badge variant="outline">67 usuários</Badge>
                  </div>
                  <div className="flex justify-between items-center p-2 bg-muted rounded">
                    <span className="text-sm">Pro - R$ 99/mês</span>
                    <Badge variant="outline">52 usuários</Badge>
                  </div>
                  <div className="flex justify-between items-center p-2 bg-muted rounded">
                    <span className="text-sm">Enterprise - R$ 199/mês</span>
                    <Badge variant="outline">15 usuários</Badge>
                  </div>
                </div>

                <Button variant="outline" className="w-full">
                  Gerenciar Planos
                </Button>
              </div>
            </div>
          </GlowCard>

          {/* Organizações */}
          <GlowCard customSize className="w-full" glowColor="orange">
            <div className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <Building className="h-6 w-6 text-orange-500" />
                <h3 className="text-xl font-semibold">Organizações</h3>
                <Badge variant="secondary">23 ativas</Badge>
              </div>
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-sm font-medium">Total Orgs</p>
                    <p className="text-2xl font-bold text-orange-500">23</p>
                  </div>
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-sm font-medium">Usuários Total</p>
                    <p className="text-2xl font-bold text-blue-500">456</p>
                  </div>
                </div>

                <Button variant="outline" className="w-full">
                  Gerenciar Organizações
                </Button>
              </div>
            </div>
          </GlowCard>

          {/* Analytics */}
          <GlowCard customSize className="w-full" glowColor="green">
            <div className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <BarChart3 className="h-6 w-6 text-green-500" />
                <h3 className="text-xl font-semibold">Analytics Avançado</h3>
                <Badge variant="outline">Dados</Badge>
              </div>
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-sm font-medium">Receita Mensal</p>
                    <p className="text-2xl font-bold text-green-500">R$ 12.4K</p>
                  </div>
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-sm font-medium">Crescimento</p>
                    <p className="text-2xl font-bold text-emerald-500">+12%</p>
                  </div>
                </div>

                <Button variant="outline" className="w-full">
                  Ver Relatórios
                </Button>
              </div>
            </div>
          </GlowCard>

          {/* Integrações */}
          <GlowCard customSize className="w-full" glowColor="blue">
            <div className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <Zap className="h-6 w-6 text-blue-500" />
                <h3 className="text-xl font-semibold">Integrações</h3>
                <Badge variant="secondary">5 ativas</Badge>
              </div>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Stripe</span>
                    <Badge variant="outline" className="text-green-500">Ativo</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Webhook</span>
                    <Badge variant="outline" className="text-green-500">Ativo</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Email Service</span>
                    <Badge variant="outline" className="text-yellow-500">Config</Badge>
                  </div>
                </div>

                <Button variant="outline" className="w-full">
                  Configurar Integrações
                </Button>
              </div>
            </div>
          </GlowCard>

          {/* API e Webhooks */}
          <GlowCard customSize className="w-full" glowColor="purple">
            <div className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <Globe className="h-6 w-6 text-indigo-500" />
                <h3 className="text-xl font-semibold">API & Webhooks</h3>
                <Badge variant="outline">Ativo</Badge>
              </div>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Rate Limit</span>
                    <span className="text-sm text-muted-foreground">1000/hora</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Webhooks Ativos</span>
                    <span className="text-sm text-muted-foreground">3</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">API Keys</span>
                    <span className="text-sm text-muted-foreground">2</span>
                  </div>
                </div>

                <Button variant="outline" className="w-full">
                  Gerenciar API
                </Button>
              </div>
            </div>
          </GlowCard>

          {/* Suporte */}
          <GlowCard customSize className="w-full" glowColor="red">
            <div className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <MessageSquare className="h-6 w-6 text-pink-500" />
                <h3 className="text-xl font-semibold">Suporte & Tickets</h3>
                <Badge variant="destructive">12 abertos</Badge>
              </div>
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-sm font-medium">Tickets Abertos</p>
                    <p className="text-2xl font-bold text-red-500">12</p>
                  </div>
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-sm font-medium">Resolvidos Hoje</p>
                    <p className="text-2xl font-bold text-green-500">8</p>
                  </div>
                </div>

                <Button variant="outline" className="w-full">
                  Central de Suporte
                </Button>
              </div>
            </div>
          </GlowCard>
        </div>

        {/* Ações Perigosas */}
        <GlowCard customSize className="w-full" glowColor="red">
          <div className="p-6">
            <div className="flex items-center space-x-3 mb-4">
              <AlertTriangle className="h-6 w-6 text-red-500" />
              <h3 className="text-xl font-semibold text-red-500">Zona de Perigo</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button variant="destructive" className="flex-1">
                Reset Sistema
              </Button>
              <Button variant="destructive" className="flex-1">
                Limpar Logs
              </Button>
              <Button variant="destructive" className="flex-1">
                Backup Completo
              </Button>
            </div>
          </div>
        </GlowCard>

        {/* Botão Salvar */}
        <div className="flex justify-end">
          <Button onClick={handleSave} disabled={saving} size="lg">
            {saving ? 'Salvando...' : 'Salvar Configurações'}
          </Button>
        </div>
      </div>
    </DashboardLayout>
  )
}