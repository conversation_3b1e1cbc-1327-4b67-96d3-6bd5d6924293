# <PERSON>ris Glow Hub

## Projeto

Sistema jurídico inteligente com IA especializada para advogados e escritórios de advocacia.

## Como executar o projeto

Certifique-se de ter Node.js & npm instalados - [instalar com nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Siga estes passos:

```sh
# Passo 1: Clone o repositório
git clone <YOUR_GIT_URL>

# Passo 2: Navegue para o diretório do projeto
cd <YOUR_PROJECT_NAME>

# Passo 3: Instale as dependências necessárias
npm i

# Passo 4: Inicie o servidor de desenvolvimento
npm run dev
```

## Tecnologias utilizadas

Este projeto foi construído com:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS
- Supabase

## Como fazer deploy

Para fazer deploy do projeto, você pode usar qualquer plataforma de hospedagem que suporte aplicações React, como:

- Vercel
- Netlify
- GitHub Pages
- Heroku

Execute `npm run build` para gerar os arquivos de produção na pasta `dist`.
